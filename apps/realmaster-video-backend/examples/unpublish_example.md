# 视频下架功能示例

## 功能概述

视频下架功能现在不仅会在数据库中更新URL添加`unpublished_`前缀，还会在文件系统层面重命名实际文件，确保完全隔离下架的视频内容。

## API 使用示例

### 1. 下架已发布的视频

```bash
# 下架视频
curl -X PATCH "http://localhost:8080/api/videos/{video_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {jwt_token}" \
  -d '{
    "action": "unpublish"
  }'
```

**执行结果：**
- 视频状态从 `Published` 变为 `Unpublished`
- 数据库中的路径从 `USER/2025-28/abc123/video.mp4` 变为 `USER/2025-28/unpublished_abc123/video.mp4`
- 实际文件从 `/var/www/media/rm_videos/USER/2025-28/abc123/video.mp4` 重命名为 `/var/www/media/rm_videos/USER/2025-28/unpublished_abc123/video.mp4`
- 缩略图文件也会同样处理

### 2. 重新发布已下架的视频

```bash
# 重新发布视频
curl -X PATCH "http://localhost:8080/api/videos/{video_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {jwt_token}" \
  -d '{
    "action": "publish"
  }'
```

**执行结果：**
- 视频状态从 `Unpublished` 变为 `Published`
- 数据库中的路径从 `USER/2025-28/unpublished_abc123/video.mp4` 变为 `USER/2025-28/abc123/video.mp4`
- 实际文件从 `/var/www/media/rm_videos/USER/2025-28/unpublished_abc123/video.mp4` 重命名为 `/var/www/media/rm_videos/USER/2025-28/abc123/video.mp4`
- 更新 `publishedAt` 时间戳

## 技术实现细节

### 文件重命名机制

1. **优先使用硬链接**：为了提高性能，系统首先尝试使用硬链接创建新文件
2. **回退到文件复制**：如果硬链接失败（如跨文件系统），则使用文件复制
3. **原子性操作**：先创建新文件，成功后再删除原文件，确保操作的原子性
4. **错误处理**：如果任何步骤失败，会清理已创建的文件并返回错误

### 目录结构变化

**下架前：**
```
/var/www/media/rm_videos/
└── USER/
    └── 2025-28/
        └── abc123/
            ├── video.mp4
            └── video_packaged/
                ├── manifest.mpd
                └── ...
```

**下架后：**
```
/var/www/media/rm_videos/
└── USER/
    └── 2025-28/
        └── unpublished_abc123/
            ├── video.mp4
            └── video_packaged/
                ├── manifest.mpd
                └── ...
```

### 安全性保障

1. **URL隔离**：客户端缓存的原URL无法访问下架内容
2. **文件系统隔离**：即使有人知道原文件路径，也无法直接访问
3. **事务性操作**：数据库更新和文件重命名在同一事务中完成
4. **错误回滚**：任何步骤失败都会回滚已执行的操作

## 注意事项

1. **权限要求**：确保应用程序对媒体目录有读写权限
2. **磁盘空间**：重命名操作可能临时需要额外的磁盘空间（如果使用文件复制）
3. **并发安全**：多个并发的下架/发布操作是安全的
4. **日志记录**：所有文件操作都会记录详细日志，便于问题排查

## 错误处理

常见错误及解决方案：

- **文件不存在**：检查原文件路径是否正确
- **权限不足**：确保应用程序对目标目录有写权限
- **磁盘空间不足**：清理磁盘空间或使用硬链接模式
- **目录创建失败**：检查父目录权限和磁盘空间
