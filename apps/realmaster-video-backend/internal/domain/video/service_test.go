package video

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/testutil"
)

func TestVideoService_CreateDraft(t *testing.T) {
	// 设置测试环境
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()
	suite.CreateTempDirs()

	// 创建service实例
	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功创建草稿视频", func(t *testing.T) {
		// 准备测试数据
		req := CreateVideoRequest{
			Title: MultilingualString{
				Zh: "测试视频创建",
				En: "Test Video Creation",
			},
			Description: MultilingualString{
				Zh: "这是一个测试视频创建请求",
				En: "This is a test video creation request",
			},
			UploaderID:             "test-uploader",
			CategoryID:             primitive.NewObjectID().Hex(),
			Tags:                   []string{"测试", "创建"},
			PropertyIDs:            []string{"prop-123"},
			ExternalURL:            "https://example.com/property/456",
			ClientID:               primitive.NewObjectID().Hex(),
			DraftVideoGouploadPath: "test-user/2025-28/abc123/video.mp4",
			DraftThumbGouploadPath: "test-user/2025-28/abc123/thumb.jpg",
		}

		// 执行创建操作
		video, err := service.CreateDraft(ctx, req)

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, video)
		assert.Equal(t, StatusDraft, video.Status)
		assert.Equal(t, req.Title.Zh, video.Title.Zh)
		assert.Equal(t, req.UploaderID, video.UploaderID)
		assert.False(t, video.ID.IsZero())

		// 验证数据库中的数据
		foundVideo, err := repo.FindByID(ctx, video.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, video.ID, foundVideo.ID)
		assert.Equal(t, StatusDraft, foundVideo.Status)
	})

	t.Run("无效分类ID创建失败", func(t *testing.T) {
		req := CreateVideoRequest{
			Title: MultilingualString{
				Zh: "测试视频",
				En: "Test Video",
			},
			UploaderID: "test-uploader",
			CategoryID: "invalid-id", // 无效的ObjectID格式
		}

		// 执行创建操作
		video, err := service.CreateDraft(ctx, req)

		// 验证结果
		assert.Error(t, err)
		assert.Nil(t, video)
	})

	t.Run("空标题创建成功但标题为空", func(t *testing.T) {
		req := CreateVideoRequest{
			// Title为空，但其他字段正常
			UploaderID: "test-uploader",
			CategoryID: primitive.NewObjectID().Hex(),
		}

		// 执行创建操作
		video, err := service.CreateDraft(ctx, req)

		// 验证结果 - 根据实际实现，可能允许空标题
		require.NoError(t, err)
		require.NotNil(t, video)
		assert.Equal(t, StatusDraft, video.Status)
		assert.Equal(t, "", video.Title.Zh) // 标题为空
		assert.Equal(t, "", video.Title.En)
	})
}

func TestVideoService_GetVideoByID(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功获取存在的视频", func(t *testing.T) {
		// 先创建一个视频
		originalVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "获取测试视频",
				En: "Get Test Video",
			},
			Description: MultilingualString{
				Zh: "测试描述",
				En: "Test Description",
			},
			Status:     StatusPublished,
			Duration:   120.5,
			UploaderID: "test-uploader",
			CategoryID: primitive.NewObjectID(),
			Tags:       []string{"测试"},
			Stats: VideoStats{
				Views:          100,
				Likes:          10,
				Collections:    5,
				Completions:    80,
				CompletionRate: "80.0%",
			},
		}

		err := repo.Create(ctx, originalVideo)
		require.NoError(t, err)

		// 通过服务获取视频
		videoResponse, err := service.GetVideoByID(ctx, originalVideo.ID.Hex())

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, videoResponse)
		assert.Equal(t, originalVideo.ID, videoResponse.ID)
		assert.Equal(t, originalVideo.Title.Zh, videoResponse.Title.Zh)
		assert.Equal(t, originalVideo.Status, videoResponse.Status)
		assert.Equal(t, originalVideo.Duration, videoResponse.Duration)
		assert.Equal(t, originalVideo.Stats.Views, videoResponse.Stats.Views)
	})

	t.Run("获取不存在的视频返回错误", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		videoResponse, err := service.GetVideoByID(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
		assert.Nil(t, videoResponse)
	})

	t.Run("无效ID格式返回错误", func(t *testing.T) {
		invalidID := "invalid-id-format"

		videoResponse, err := service.GetVideoByID(ctx, invalidID)

		assert.Error(t, err)
		assert.Nil(t, videoResponse)
	})
}

func TestVideoService_PublishVideo(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功发布草稿视频", func(t *testing.T) {
		// 创建草稿视频
		draftVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "待发布视频",
				En: "Video to Publish",
			},
			Status:     StatusDraft,
			UploaderID: "test-uploader",
		}

		err := repo.Create(ctx, draftVideo)
		require.NoError(t, err)

		// 发布视频
		err = service.PublishVideo(ctx, draftVideo.ID.Hex())

		// 验证结果
		require.NoError(t, err)

		// 验证状态已更新为pending
		updatedVideo, err := repo.FindByID(ctx, draftVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, StatusPending, updatedVideo.Status)
	})

	t.Run("发布已发布的视频失败", func(t *testing.T) {
		// 创建已发布的视频
		publishedVideo := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "已发布视频",
				En: "Published Video",
			},
			Status:     StatusPublished,
			UploaderID: "test-uploader",
		}

		err := repo.Create(ctx, publishedVideo)
		require.NoError(t, err)

		// 尝试再次发布
		err = service.PublishVideo(ctx, publishedVideo.ID.Hex())

		// 验证结果
		assert.Error(t, err)
		// 检查错误是否包含预期的错误信息
		assert.Contains(t, err.Error(), "视频状态不正确")
	})

	t.Run("发布不存在的视频失败", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		err := service.PublishVideo(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})
}

func TestVideoService_FindVideos(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("按状态过滤查找视频", func(t *testing.T) {
		// 创建不同状态的视频
		draftVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "草稿视频", En: "Draft Video"},
			Status:     StatusDraft,
			UploaderID: "test-user",
		}
		publishedVideo := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "已发布视频", En: "Published Video"},
			Status:     StatusPublished,
			UploaderID: "test-user",
		}

		err := repo.Create(ctx, draftVideo)
		require.NoError(t, err)
		err = repo.Create(ctx, publishedVideo)
		require.NoError(t, err)

		// 查找已发布的视频
		filter := VideoFilter{
			Status: []string{StatusPublished},
			Page:   1,
			Limit:  10,
		}

		videos, pagination, err := service.FindVideos(ctx, filter)

		require.NoError(t, err)
		require.NotNil(t, pagination)
		assert.Equal(t, int64(1), pagination.TotalItems)
		assert.Len(t, videos, 1)
		assert.Equal(t, StatusPublished, videos[0].Status)
		assert.Equal(t, "已发布视频", videos[0].Title.Zh)
	})

	t.Run("空结果查询", func(t *testing.T) {
		filter := VideoFilter{
			Status: []string{"nonexistent-status"},
			Page:   1,
			Limit:  10,
		}

		videos, pagination, err := service.FindVideos(ctx, filter)

		require.NoError(t, err)
		require.NotNil(t, pagination)
		assert.Equal(t, int64(0), pagination.TotalItems)
		assert.Len(t, videos, 0)
	})
}

func TestVideoService_UpdateStats(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功更新视频统计", func(t *testing.T) {
		suite.CleanupDatabase()

		// 先创建一个视频
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "统计测试视频",
				En: "Stats Test Video",
			},
			Status:     StatusPublished,
			UploaderID: "test-uploader",
			Stats: VideoStats{
				Views:          100,
				Likes:          10,
				Collections:    5,
				Completions:    80,
				CompletionRate: "80.0%",
			},
		}

		err := repo.Create(ctx, video)
		require.NoError(t, err)

		// 更新统计数据
		views := int64(5)
		likes := int64(2)
		collections := int64(1)
		completions := int64(3)

		req := UpdateStatsRequest{
			Views:       &views,
			Likes:       &likes,
			Collections: &collections,
			Completions: &completions,
		}

		err = service.UpdateStats(ctx, video.ID.Hex(), req)

		// 验证结果
		require.NoError(t, err)
	})

	t.Run("更新不存在视频的统计失败", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		views := int64(5)
		req := UpdateStatsRequest{
			Views: &views,
		}

		err := service.UpdateStats(ctx, nonExistentID, req)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})

	t.Run("部分字段更新", func(t *testing.T) {
		suite.CleanupDatabase()

		// 创建测试视频
		video := &Video{
			ID:         primitive.NewObjectID(),
			Title:      MultilingualString{Zh: "部分更新测试", En: "Partial Update Test"},
			Status:     StatusPublished,
			UploaderID: "test-uploader",
			Stats:      VideoStats{Views: 100, Likes: 10},
		}

		err := repo.Create(ctx, video)
		require.NoError(t, err)

		// 只更新views字段
		views := int64(50)
		req := UpdateStatsRequest{
			Views: &views,
			// 其他字段为nil，不应该被更新
		}

		err = service.UpdateStats(ctx, video.ID.Hex(), req)
		require.NoError(t, err)
	})
}

func TestVideoService_DeleteVideo(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功删除视频", func(t *testing.T) {
		suite.CleanupDatabase()

		// 先创建一个视频
		video := &Video{
			ID: primitive.NewObjectID(),
			Title: MultilingualString{
				Zh: "待删除视频",
				En: "Video to Delete",
			},
			Status:     StatusDraft,
			UploaderID: "test-uploader",
		}

		err := repo.Create(ctx, video)
		require.NoError(t, err)

		// 删除视频
		err = service.DeleteVideo(ctx, video.ID.Hex())

		// 验证结果
		require.NoError(t, err)

		// 验证视频已被删除
		_, err = repo.FindByID(ctx, video.ID.Hex())
		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})

	t.Run("删除不存在的视频失败", func(t *testing.T) {
		nonExistentID := primitive.NewObjectID().Hex()

		err := service.DeleteVideo(ctx, nonExistentID)

		assert.Error(t, err)
		assert.Equal(t, ErrVideoNotFound, err)
	})

	t.Run("删除无效ID格式失败", func(t *testing.T) {
		invalidID := "invalid-id-format"

		err := service.DeleteVideo(ctx, invalidID)

		assert.Error(t, err)
		// 应该是ID格式错误，不是视频未找到错误
		assert.NotEqual(t, ErrVideoNotFound, err)
	})
}

func TestVideoService_UpdateVideo(t *testing.T) {
	suite := testutil.SetupTestSuite(t)
	defer suite.CleanupDatabase()

	repo := NewRepository()
	service := NewService(repo, suite.Config)
	ctx := context.Background()

	t.Run("成功更新草稿视频的元数据", func(t *testing.T) {
		// 1. Setup: 创建一个初始视频
		initialVideo := &Video{
			ID:     primitive.NewObjectID(),
			Title:  MultilingualString{Zh: "原始标题", En: "Original Title"},
			Status: StatusDraft,
		}
		err := repo.Create(ctx, initialVideo)
		require.NoError(t, err)

		// 2. Prepare Request: 准备更新请求
		newTitle := "更新后的标题"
		req := UpdateVideoRequest{
			ID:     initialVideo.ID,
			Action: "save_as_draft", // 保持草稿状态
			Metadata: map[string]interface{}{
				"title": MultilingualString{
					Zh: newTitle,
					En: "Updated Title",
				},
			},
		}

		// 3. Execute: 执行更新操作
		updatedVideo, err := service.UpdateVideo(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, updatedVideo)

		// 4. Assert: 验证结果
		// a. 验证返回的视频对象
		assert.Equal(t, newTitle, updatedVideo.Title.Zh)
		assert.Equal(t, StatusDraft, updatedVideo.Status, "状态不应该改变")

		// b. 验证数据库中的数据是否真的被更新了
		foundVideo, err := repo.FindByID(ctx, initialVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, newTitle, foundVideo.Title.Zh)
		assert.Equal(t, StatusDraft, foundVideo.Status)
	})

	t.Run("成功发布一个草稿视频", func(t *testing.T) {
		// 1. Setup: 创建一个草稿状态的视频
		draftVideo := &Video{
			ID:     primitive.NewObjectID(),
			Title:  MultilingualString{Zh: "待发布视频", En: "To Be Published"},
			Status: StatusDraft,
		}
		err := repo.Create(ctx, draftVideo)
		require.NoError(t, err)

		// 2. Prepare Request: 准备发布请求
		req := UpdateVideoRequest{
			ID:     draftVideo.ID,
			Action: "publish",
		}

		// 3. Execute: 执行更新操作
		updatedVideo, err := service.UpdateVideo(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, updatedVideo)

		// 4. Assert: 验证状态已变为Pending
		assert.Equal(t, StatusPending, updatedVideo.Status, "状态应该变为Pending")
		assert.Nil(t, updatedVideo.PublishedAt, "发布时间不应该在此时设置")

		// b. 验证数据库中的数据
		foundVideo, err := repo.FindByID(ctx, draftVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, StatusPending, foundVideo.Status)
	})

	t.Run("成功下架一个已发布的视频", func(t *testing.T) {
		// 1. Setup: 创建一个已发布的视频
		publishedVideo := &Video{
			ID:                     primitive.NewObjectID(),
			Title:                  MultilingualString{Zh: "已发布视频", En: "Published Video"},
			Status:                 StatusPublished,
			FinalVideoGouploadPath: "test-user/2025-28/abc123/video.mp4",
			FinalThumbGouploadPath: "test-user/2025-28/abc123/thumb.jpg",
		}
		err := repo.Create(ctx, publishedVideo)
		require.NoError(t, err)

		// 创建测试文件（模拟实际文件存在）
		// 使用与配置文件一致的路径
		testVideoDir := "/var/www/media/rm_videos/test-user/2025-28/abc123"
		testThumbDir := "/var/www/media/rm_thumbnails/test-user/2025-28/abc123"
		err = os.MkdirAll(testVideoDir, 0755)
		require.NoError(t, err)
		err = os.MkdirAll(testThumbDir, 0755)
		require.NoError(t, err)

		testVideoFile := filepath.Join(testVideoDir, "video.mp4")
		testThumbFile := filepath.Join(testThumbDir, "thumb.jpg")
		err = os.WriteFile(testVideoFile, []byte("test video content"), 0644)
		require.NoError(t, err)
		err = os.WriteFile(testThumbFile, []byte("test thumb content"), 0644)
		require.NoError(t, err)

		// 清理函数
		defer func() {
			os.RemoveAll("/var/www/media/rm_videos/test-user")
			os.RemoveAll("/var/www/media/rm_thumbnails/test-user")
		}()

		// 2. Prepare Request: 准备下架请求
		req := UpdateVideoRequest{
			ID:     publishedVideo.ID,
			Action: "unpublish",
		}

		// 3. Execute: 执行更新操作
		updatedVideo, err := service.UpdateVideo(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, updatedVideo)

		// 4. Assert: 验证状态和路径
		expectedVideoPath := "test-user/2025-28/unpublished_abc123/video.mp4"
		expectedThumbPath := "test-user/2025-28/unpublished_abc123/thumb.jpg"

		assert.Equal(t, StatusUnpublished, updatedVideo.Status, "状态应该变为Unpublished")
		assert.Equal(t, expectedVideoPath, updatedVideo.FinalVideoGouploadPath, "视频路径应该添加前缀")
		assert.Equal(t, expectedThumbPath, updatedVideo.FinalThumbGouploadPath, "封面路径应该添加前缀")

		// 5. 验证实际文件已被重命名
		// 原文件应该不存在
		_, err = os.Stat(testVideoFile)
		assert.True(t, os.IsNotExist(err), "原视频文件应该被删除")
		_, err = os.Stat(testThumbFile)
		assert.True(t, os.IsNotExist(err), "原缩略图文件应该被删除")

		// 新文件应该存在
		newVideoFile := "/var/www/media/rm_videos/test-user/2025-28/unpublished_abc123/video.mp4"
		newThumbFile := "/var/www/media/rm_thumbnails/test-user/2025-28/unpublished_abc123/thumb.jpg"
		_, err = os.Stat(newVideoFile)
		assert.NoError(t, err, "新视频文件应该存在")
		_, err = os.Stat(newThumbFile)
		assert.NoError(t, err, "新缩略图文件应该存在")

		// 验证文件内容
		videoContent, err := os.ReadFile(newVideoFile)
		require.NoError(t, err)
		assert.Equal(t, "test video content", string(videoContent))
		thumbContent, err := os.ReadFile(newThumbFile)
		require.NoError(t, err)
		assert.Equal(t, "test thumb content", string(thumbContent))

		// 6. 验证数据库中的数据
		foundVideo, err := repo.FindByID(ctx, publishedVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, StatusUnpublished, foundVideo.Status)
		assert.Equal(t, expectedVideoPath, foundVideo.FinalVideoGouploadPath)
		assert.Equal(t, expectedThumbPath, foundVideo.FinalThumbGouploadPath)
	})

	t.Run("成功重新发布一个已下架的视频", func(t *testing.T) {
		// 1. Setup: 创建一个已下架的视频
		unpublishedPath := "test-user/2025-28/unpublished_abc123/video.mp4"
		unpublishedVideo := &Video{
			ID:                     primitive.NewObjectID(),
			Title:                  MultilingualString{Zh: "已下架视频", En: "Unpublished Video"},
			Status:                 StatusUnpublished,
			FinalVideoGouploadPath: unpublishedPath,
		}
		err := repo.Create(ctx, unpublishedVideo)
		require.NoError(t, err)

		// 创建下架状态的测试文件
		unpublishedVideoDir := "/var/www/media/rm_videos/test-user/2025-28/unpublished_abc123"
		err = os.MkdirAll(unpublishedVideoDir, 0755)
		require.NoError(t, err)

		unpublishedVideoFile := filepath.Join(unpublishedVideoDir, "video.mp4")
		err = os.WriteFile(unpublishedVideoFile, []byte("unpublished video content"), 0644)
		require.NoError(t, err)

		// 清理函数
		defer func() {
			os.RemoveAll("/var/www/media/rm_videos/test-user")
		}()

		// 2. Prepare Request: 准备重新发布请求
		req := UpdateVideoRequest{
			ID:     unpublishedVideo.ID,
			Action: "publish",
		}

		// 3. Execute: 执行更新操作
		updatedVideo, err := service.UpdateVideo(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, updatedVideo)

		// 4. Assert: 验证状态和路径
		expectedPath := "test-user/2025-28/abc123/video.mp4"
		assert.Equal(t, StatusPublished, updatedVideo.Status, "状态应该变为Published")
		assert.Equal(t, expectedPath, updatedVideo.FinalVideoGouploadPath, "视频路径应该移除前缀")
		assert.NotNil(t, updatedVideo.PublishedAt, "重新发布时应该更新发布时间")

		// 5. 验证实际文件已被重命名
		// 原下架文件应该不存在
		_, err = os.Stat(unpublishedVideoFile)
		assert.True(t, os.IsNotExist(err), "原下架视频文件应该被删除")

		// 新发布文件应该存在
		publishedVideoFile := "/var/www/media/rm_videos/test-user/2025-28/abc123/video.mp4"
		_, err = os.Stat(publishedVideoFile)
		assert.NoError(t, err, "重新发布的视频文件应该存在")

		// 验证文件内容
		videoContent, err := os.ReadFile(publishedVideoFile)
		require.NoError(t, err)
		assert.Equal(t, "unpublished video content", string(videoContent))

		// 6. 验证数据库中的数据
		foundVideo, err := repo.FindByID(ctx, unpublishedVideo.ID.Hex())
		require.NoError(t, err)
		assert.Equal(t, StatusPublished, foundVideo.Status)
		assert.Equal(t, expectedPath, foundVideo.FinalVideoGouploadPath)
	})
}
