package uploader

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"realmaster-video-backend/internal/platform/logger"

	levelStore "github.com/real-rm/golevelstore"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
)

// lazyOpenerReader is an io.Reader that opens a file on the first Read call.
// It does not implement io.Seeker or io.Closer. This forces the downstream
// goupload library to handle the stream by buffering it to a temporary file,
// which avoids lifecycle conflicts over the file handle.
type lazyOpenerReader struct {
	filePath string
	file     *os.File
	mu       sync.Mutex
}

// Read opens the file if not already opened, and then reads from it. It is thread-safe.
func (r *lazyOpenerReader) Read(p []byte) (n int, err error) {
	r.mu.Lock()
	// If the file is already open, we can read without unlocking immediately.
	// If not, we open it. After this block, the file is guaranteed to be open
	// if no error occurred.
	if r.file == nil {
		var openErr error
		r.file, openErr = os.Open(r.filePath)
		if openErr != nil {
			r.mu.Unlock() // Unlock before returning error
			return 0, fmt.Errorf("lazy open for read failed for %s: %w", r.filePath, openErr)
		}
	}
	r.mu.Unlock()

	// Now we can read from the file. The file handle is not closed by this struct,
	// it will be garbage collected or closed by the OS when the process exits.
	// This is acceptable as these are short-lived readers for an upload process.
	return r.file.Read(p)
}

// GoUploadService 封装 goupload 功能
type GoUploadService struct {
	// 草稿阶段统计更新器
	videoDraftStatsUpdater     goupload.StatsUpdater
	thumbnailDraftStatsUpdater goupload.StatsUpdater
	// 最终阶段统计更新器
	videoFinalStatsUpdater     goupload.StatsUpdater
	thumbnailFinalStatsUpdater goupload.StatsUpdater
	site                       string
}

// NewGoUploadService 创建新的 goupload 服务实例
func NewGoUploadService(site string) (*GoUploadService, error) {
	// 获取统计集合
	statsColl := gomongo.Coll("realmaster_video", "upload_stats")

	// 创建草稿阶段统计更新器
	videoDraftStatsUpdater, err := goupload.NewStatsUpdater(site, "video_draft", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建视频草稿统计更新器失败: %w", err)
	}

	thumbnailDraftStatsUpdater, err := goupload.NewStatsUpdater(site, "thumbnail_draft", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建缩略图草稿统计更新器失败: %w", err)
	}

	// 创建最终阶段统计更新器
	videoFinalStatsUpdater, err := goupload.NewStatsUpdater(site, "video_final", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建视频最终统计更新器失败: %w", err)
	}

	thumbnailFinalStatsUpdater, err := goupload.NewStatsUpdater(site, "thumbnail_final", statsColl)
	if err != nil {
		return nil, fmt.Errorf("创建缩略图最终统计更新器失败: %w", err)
	}

	return &GoUploadService{
		videoDraftStatsUpdater:     videoDraftStatsUpdater,
		thumbnailDraftStatsUpdater: thumbnailDraftStatsUpdater,
		videoFinalStatsUpdater:     videoFinalStatsUpdater,
		thumbnailFinalStatsUpdater: thumbnailFinalStatsUpdater,
		site:                       site,
	}, nil
}

// UploadVideo 上传视频文件
func (s *GoUploadService) UploadVideo(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	logger.Log.Info("开始上传视频文件",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("size", size),
	)

	result, err := goupload.Upload(
		ctx,
		s.videoDraftStatsUpdater,
		s.site,
		"video_draft",
		userID,
		reader,
		filename,
		size,
	)

	if err != nil {
		logger.Log.Error("视频上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return nil, s.handleUploadError(err)
	}

	logger.Log.Info("视频上传成功",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("path", result.Path),
	)

	return result, nil
}

// UploadVideoDraft 上传视频文件到草稿目录（别名方法）
func (s *GoUploadService) UploadVideoDraft(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	return s.UploadVideo(ctx, userID, reader, filename, size)
}

// UploadThumbnailDraft 上传缩略图文件到草稿目录（别名方法）
func (s *GoUploadService) UploadThumbnailDraft(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	return s.UploadThumbnail(ctx, userID, reader, filename, size)
}

// UploadThumbnail 上传缩略图文件
func (s *GoUploadService) UploadThumbnail(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	logger.Log.Info("开始上传缩略图文件",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("size", size),
	)

	result, err := goupload.Upload(
		ctx,
		s.thumbnailDraftStatsUpdater,
		s.site,
		"thumbnail_draft",
		userID,
		reader,
		filename,
		size,
	)

	if err != nil {
		logger.Log.Error("缩略图上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return nil, s.handleUploadError(err)
	}

	logger.Log.Info("缩略图上传成功",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("path", result.Path),
	)

	return result, nil
}

// InitiateChunkedVideoUpload 初始化分块视频上传
func (s *GoUploadService) InitiateChunkedVideoUpload(ctx context.Context, userID, filename string, totalSize int64) (string, error) {
	logger.Log.Info("初始化分块视频上传",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("totalSize", totalSize),
	)

	provider := &goupload.LevelStoreProvider{}
	uploadID, err := goupload.InitiateChunkedUpload(
		ctx,
		provider,
		s.site,
		"video_draft",
		userID,
		filename,
		totalSize,
	)

	if err != nil {
		logger.Log.Error("初始化分块上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return "", fmt.Errorf("初始化分块上传失败: %w", err)
	}

	logger.Log.Info("分块上传初始化成功",
		logger.String("userID", userID),
		logger.String("uploadID", uploadID),
	)

	return uploadID, nil
}

// UploadChunk 上传文件分块
func (s *GoUploadService) UploadChunk(ctx context.Context, uploadID string, chunkNumber int, chunkReader io.Reader) error {
	logger.Log.Debug("上传文件分块",
		logger.String("uploadID", uploadID),
		logger.Int("chunkNumber", chunkNumber),
	)

	err := goupload.UploadChunk(ctx, uploadID, chunkNumber, chunkReader)
	if err != nil {
		logger.Log.Error("分块上传失败",
			logger.String("uploadID", uploadID),
			logger.Int("chunkNumber", chunkNumber),
			logger.Error(err),
		)
		return fmt.Errorf("分块上传失败: %w", err)
	}

	return nil
}

// CompleteChunkedVideoUpload 完成分块视频上传
func (s *GoUploadService) CompleteChunkedVideoUpload(ctx context.Context, uploadID string, expectedChunks int) (*goupload.UploadResult, error) {
	logger.Log.Info("完成分块视频上传",
		logger.String("uploadID", uploadID),
		logger.Int("expectedChunks", expectedChunks),
	)

	// S3 provider map (如果需要的话)
	s3ProviderMap := make(map[string]levelStore.S3ProviderConfig) // 根据实际需要配置

	result, err := goupload.CompleteChunkedUpload(
		ctx,
		s.videoDraftStatsUpdater,
		uploadID,
		expectedChunks,
		s3ProviderMap,
	)

	if err != nil {
		logger.Log.Error("完成分块上传失败",
			logger.String("uploadID", uploadID),
			logger.Error(err),
		)
		return nil, fmt.Errorf("完成分块上传失败: %w", err)
	}

	logger.Log.Info("分块上传完成",
		logger.String("uploadID", uploadID),
		logger.String("path", result.Path),
	)

	return result, nil
}

// DeleteFile 删除文件
func (s *GoUploadService) DeleteFile(ctx context.Context, entryName, relativePath string) error {
	logger.Log.Info("删除文件",
		logger.String("entryName", entryName),
		logger.String("relativePath", relativePath),
	)

	var statsUpdater goupload.StatsUpdater
	switch entryName {
	case "video_draft":
		statsUpdater = s.videoDraftStatsUpdater
	case "thumbnail_draft":
		statsUpdater = s.thumbnailDraftStatsUpdater
	case "video_final":
		statsUpdater = s.videoFinalStatsUpdater
	case "thumbnail_final":
		statsUpdater = s.thumbnailFinalStatsUpdater
	default:
		return fmt.Errorf("不支持的文件类型: %s", entryName)
	}

	result, err := goupload.Delete(ctx, statsUpdater, s.site, entryName, relativePath)
	if err != nil {
		logger.Log.Error("删除文件失败",
			logger.String("entryName", entryName),
			logger.String("relativePath", relativePath),
			logger.Error(err),
		)
		return fmt.Errorf("删除文件失败: %w", err)
	}

	if result.IsPartialDelete {
		logger.Log.Warn("文件部分删除",
			logger.String("relativePath", relativePath),
			logger.Int("deletedCount", len(result.DeletedPaths)),
			logger.Int("failedCount", len(result.FailedPaths)),
		)
	} else {
		logger.Log.Info("文件删除成功",
			logger.String("relativePath", relativePath),
			logger.Int("deletedCount", len(result.DeletedPaths)),
		)
	}

	return nil
}

// handleUploadError 处理上传错误，提供更友好的错误信息
func (s *GoUploadService) handleUploadError(err error) error {
	errMsg := err.Error()

	// 验证错误 - 客户端错误
	if strings.Contains(errMsg, "filename") ||
		strings.Contains(errMsg, "exceeds limit") ||
		strings.Contains(errMsg, "cannot be zero") {
		return fmt.Errorf("文件验证失败: %w", err)
	}

	// 写入错误 - 服务器错误
	if strings.Contains(errMsg, "write to") ||
		strings.Contains(errMsg, "failed to write") {
		return fmt.Errorf("文件写入失败: %w", err)
	}

	// 回滚错误 - 严重服务器错误
	if strings.Contains(errMsg, "rollback") {
		return fmt.Errorf("文件上传回滚失败: %w", err)
	}

	return err
}

// === 最终阶段上传方法 ===

// UploadVideoFinal 上传视频文件到最终目录
func (s *GoUploadService) UploadVideoFinal(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	logger.Log.Info("开始上传最终视频文件",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("size", size),
	)

	result, err := goupload.Upload(
		ctx,
		s.videoFinalStatsUpdater,
		s.site,
		"video_final",
		userID,
		reader,
		filename,
		size,
	)

	if err != nil {
		logger.Log.Error("最终视频上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return nil, s.handleUploadError(err)
	}

	logger.Log.Info("最终视频上传成功",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("path", result.Path),
	)

	return result, nil
}

// UploadThumbnailFinal 上传缩略图文件到最终目录
func (s *GoUploadService) UploadThumbnailFinal(ctx context.Context, userID string, reader io.Reader, filename string, size int64) (*goupload.UploadResult, error) {
	logger.Log.Info("开始上传最终缩略图文件",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.Int64("size", size),
	)

	result, err := goupload.Upload(
		ctx,
		s.thumbnailFinalStatsUpdater,
		s.site,
		"thumbnail_final",
		userID,
		reader,
		filename,
		size,
	)

	if err != nil {
		logger.Log.Error("最终缩略图上传失败",
			logger.String("userID", userID),
			logger.String("filename", filename),
			logger.Error(err),
		)
		return nil, s.handleUploadError(err)
	}

	logger.Log.Info("最终缩略图上传成功",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("path", result.Path),
	)

	return result, nil
}

// === 目录上传方法 ===

// UploadDirectoryToFinal 上传整个目录到最终位置（供Worker使用）
func (s *GoUploadService) UploadDirectoryToFinal(ctx context.Context, userID, directoryName, localDirPath string) (*goupload.DirectoryUploadResult, error) {
	return s.UploadDirectory(ctx, "video_final", userID, directoryName, localDirPath)
}

// UploadDirectory 上传整个目录到最终位置（供Worker使用）
func (s *GoUploadService) UploadDirectory(ctx context.Context, entryName, userID, directoryName, localDirPath string) (*goupload.DirectoryUploadResult, error) {
	var statsUpdater goupload.StatsUpdater
	switch entryName {
	case "video_final":
		statsUpdater = s.videoFinalStatsUpdater
	case "thumbnail_final":
		statsUpdater = s.thumbnailFinalStatsUpdater
	default:
		return nil, fmt.Errorf("不支持的目录上传类型: %s", entryName)
	}

	// 构建目录上传请求
	request := &goupload.DirectoryUploadRequest{
		Site:          s.site,
		EntryName:     entryName,
		UserID:        userID,
		DirectoryName: directoryName,
		Files:         []goupload.DirectoryFileEntry{},
	}

	// 收集文件信息，但不立即打开文件
	type fileInfo struct {
		fullPath     string
		relativePath string
		size         int64
		modTime      time.Time
	}

	var fileInfos []fileInfo

	// 遍历本地目录，收集文件信息
	err := filepath.Walk(localDirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || info.IsDir() {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(localDirPath, path)
		if err != nil {
			return err
		}

		fileInfos = append(fileInfos, fileInfo{
			fullPath:     path,
			relativePath: filepath.ToSlash(relPath), // 统一使用正斜杠
			size:         info.Size(),
			modTime:      info.ModTime(),
		})

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("遍历目录失败: %w", err)
	}

	// 为每个文件创建DirectoryFileEntry，使用延迟打开的文件句柄
	for _, fInfo := range fileInfos {
		// 创建一个延迟打开文件的Reader
		fileReader := &lazyOpenerReader{
			filePath: fInfo.fullPath,
		}

		request.Files = append(request.Files, goupload.DirectoryFileEntry{
			RelativePath: fInfo.relativePath,
			Reader:       fileReader,
			Size:         fInfo.size,
			ModTime:      &fInfo.modTime,
		})
	}

	// 执行目录上传
	return goupload.UploadDirectory(ctx, statsUpdater, request)
}

// 注意：移除了GetLocalPath方法，Worker应该从视频字段中获取goupload路径

// === 删除方法的便捷接口 ===

// DeleteVideoDraft 删除草稿视频文件
func (s *GoUploadService) DeleteVideoDraft(ctx context.Context, gouploadPath string) error {
	return s.DeleteFile(ctx, "video_draft", gouploadPath)
}

// DeleteThumbnailDraft 删除草稿缩略图文件
func (s *GoUploadService) DeleteThumbnailDraft(ctx context.Context, gouploadPath string) error {
	return s.DeleteFile(ctx, "thumbnail_draft", gouploadPath)
}

// DeleteVideoFinal 删除最终视频文件
func (s *GoUploadService) DeleteVideoFinal(ctx context.Context, gouploadPath string) error {
	return s.DeleteFile(ctx, "video_final", gouploadPath)
}

// DeleteThumbnailFinal 删除最终缩略图文件
func (s *GoUploadService) DeleteThumbnailFinal(ctx context.Context, gouploadPath string) error {
	return s.DeleteFile(ctx, "thumbnail_final", gouploadPath)
}

// === URL生成方法 ===

// GetPreviewURL 根据goupload路径和prefix生成预览URL
// 注意：现在使用UploadResult中的prefix字段，而不是硬编码
func (s *GoUploadService) GetPreviewURL(prefix, gouploadPath, baseURL string) string {
	if gouploadPath == "" || prefix == "" {
		return ""
	}

	// 确保基础URL包含协议
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}

	// 直接使用配置中的prefix，格式: prefix + "/" + gouploadPath
	return fmt.Sprintf("%s%s/%s", baseURL, prefix, gouploadPath)
}

// GetDraftVideoURL 获取草稿视频预览URL
// 注意：现在需要传入prefix参数，从UploadResult中获取
func (s *GoUploadService) GetDraftVideoURL(prefix, gouploadPath, baseURL string) string {
	return s.GetPreviewURL(prefix, gouploadPath, baseURL)
}

// GetDraftThumbnailURL 获取草稿缩略图预览URL
// 注意：现在需要传入prefix参数，从UploadResult中获取
func (s *GoUploadService) GetDraftThumbnailURL(prefix, gouploadPath, baseURL string) string {
	return s.GetPreviewURL(prefix, gouploadPath, baseURL)
}

// GetFinalVideoURL 获取最终视频预览URL
// 注意：现在需要传入prefix参数，从UploadResult中获取
func (s *GoUploadService) GetFinalVideoURL(prefix, gouploadPath, baseURL string) string {
	return s.GetPreviewURL(prefix, gouploadPath, baseURL)
}

// GetFinalThumbnailURL 获取最终缩略图预览URL
// 注意：现在需要传入prefix参数，从UploadResult中获取
func (s *GoUploadService) GetFinalThumbnailURL(prefix, gouploadPath, baseURL string) string {
	return s.GetPreviewURL(prefix, gouploadPath, baseURL)
}

// === 文件重命名方法（用于下架/重新发布）===

// RenameVideoFinal 重命名最终视频文件（用于下架/重新发布）
// 通过复制文件到新路径，然后删除旧文件来实现重命名
func (s *GoUploadService) RenameVideoFinal(ctx context.Context, oldGouploadPath, newGouploadPath string) error {
	return s.renameFile(ctx, "video_final", oldGouploadPath, newGouploadPath)
}

// RenameThumbnailFinal 重命名最终缩略图文件（用于下架/重新发布）
func (s *GoUploadService) RenameThumbnailFinal(ctx context.Context, oldGouploadPath, newGouploadPath string) error {
	return s.renameFile(ctx, "thumbnail_final", oldGouploadPath, newGouploadPath)
}

// RenameVideoDirectory 重命名视频目录（包含所有m3u8文件和视频片段）
// 这个方法用于视频下架时重命名整个视频处理后的目录
func (s *GoUploadService) RenameVideoDirectory(ctx context.Context, oldGouploadPath, newGouploadPath string) error {
	if oldGouploadPath == "" || newGouploadPath == "" {
		return fmt.Errorf("源路径和目标路径不能为空")
	}

	if oldGouploadPath == newGouploadPath {
		logger.Log.Info("源路径和目标路径相同，跳过重命名",
			logger.String("path", oldGouploadPath))
		return nil
	}

	logger.Log.Info("开始重命名视频目录",
		logger.String("oldPath", oldGouploadPath),
		logger.String("newPath", newGouploadPath),
	)

	// 构建本地目录路径
	localBasePath := "/var/www/media/rm_videos" // TODO: 从config.toml获取

	// 从goupload路径中提取目录部分
	// 例如：user/2025-28/abc123/video.m3u8 -> user/2025-28/abc123
	oldDirPath := filepath.Dir(oldGouploadPath)
	newDirPath := filepath.Dir(newGouploadPath)

	oldLocalDirPath := filepath.Join(localBasePath, oldDirPath)
	newLocalDirPath := filepath.Join(localBasePath, newDirPath)

	// 检查源目录是否存在
	if _, err := os.Stat(oldLocalDirPath); os.IsNotExist(err) {
		logger.Log.Error("源目录不存在",
			logger.String("path", oldLocalDirPath))
		return fmt.Errorf("源目录不存在: %s", oldLocalDirPath)
	}

	// 确保目标父目录存在
	newParentDir := filepath.Dir(newLocalDirPath)
	if err := os.MkdirAll(newParentDir, 0755); err != nil {
		logger.Log.Error("创建目标父目录失败",
			logger.String("dir", newParentDir),
			logger.Error(err))
		return fmt.Errorf("创建目标父目录失败: %w", err)
	}

	// 直接重命名整个目录
	if err := os.Rename(oldLocalDirPath, newLocalDirPath); err != nil {
		logger.Log.Error("重命名目录失败",
			logger.String("oldDir", oldLocalDirPath),
			logger.String("newDir", newLocalDirPath),
			logger.Error(err))
		return fmt.Errorf("重命名目录失败: %w", err)
	}

	logger.Log.Info("视频目录重命名成功",
		logger.String("oldPath", oldGouploadPath),
		logger.String("newPath", newGouploadPath),
	)

	return nil
}

// renameFile 通用的文件重命名方法
//
// goupload库没有提供Rename方法，所以我们自己实现文件重命名功能。
//
// 实现策略（按优先级）：
// 1. os.Rename() - 最高效，原子操作
// 2. os.Link() + os.Remove() - 硬链接+删除，适用于同文件系统
// 3. 文件复制 + os.Remove() - 最后的备选方案
//
// 注意：当前实现仅适用于本地存储。如果将来使用S3等云存储，
// 需要根据存储类型选择相应的重命名策略。
func (s *GoUploadService) renameFile(ctx context.Context, entryName, oldGouploadPath, newGouploadPath string) error {
	if oldGouploadPath == "" || newGouploadPath == "" {
		return fmt.Errorf("源路径和目标路径不能为空")
	}

	if oldGouploadPath == newGouploadPath {
		logger.Log.Info("源路径和目标路径相同，跳过重命名",
			logger.String("path", oldGouploadPath))
		return nil
	}

	logger.Log.Info("开始重命名文件",
		logger.String("entryName", entryName),
		logger.String("oldPath", oldGouploadPath),
		logger.String("newPath", newGouploadPath),
	)

	// 1. 验证文件类型
	switch entryName {
	case "video_final", "thumbnail_final":
		// 支持的类型
	default:
		return fmt.Errorf("不支持的文件类型: %s", entryName)
	}

	// 2. 构建本地文件路径（基于goupload配置）
	// TODO: 这里应该从goupload配置中读取路径，而不是硬编码
	// 当前实现是临时方案，因为goupload库没有提供Rename方法
	var localBasePath string
	switch entryName {
	case "video_final":
		localBasePath = "/var/www/media/rm_videos" // TODO: 从config.toml获取
	case "thumbnail_final":
		localBasePath = "/var/www/media/rm_thumbnails" // TODO: 从config.toml获取
	}

	oldLocalPath := filepath.Join(localBasePath, oldGouploadPath)
	newLocalPath := filepath.Join(localBasePath, newGouploadPath)

	// 3. 检查源文件是否存在
	if _, err := os.Stat(oldLocalPath); os.IsNotExist(err) {
		logger.Log.Error("源文件不存在",
			logger.String("path", oldLocalPath))
		return fmt.Errorf("源文件不存在: %s", oldLocalPath)
	}

	// 4. 确保目标目录存在
	newDir := filepath.Dir(newLocalPath)
	if err := os.MkdirAll(newDir, 0755); err != nil {
		logger.Log.Error("创建目标目录失败",
			logger.String("dir", newDir),
			logger.Error(err))
		return fmt.Errorf("创建目标目录失败: %w", err)
	}

	// 5. 直接重命名文件（最高效的方法）
	if err := os.Rename(oldLocalPath, newLocalPath); err != nil {
		// 如果直接重命名失败（可能跨文件系统），使用复制+删除的方式
		logger.Log.Info("直接重命名失败，使用复制+删除方式", logger.Error(err))

		// 先尝试硬链接（同文件系统内最高效）
		if err := os.Link(oldLocalPath, newLocalPath); err != nil {
			// 硬链接也失败，使用文件复制
			logger.Log.Info("硬链接失败，使用文件复制", logger.Error(err))
			if err := s.copyFile(oldLocalPath, newLocalPath); err != nil {
				return fmt.Errorf("复制文件失败: %w", err)
			}
		}

		// 删除原文件
		if err := os.Remove(oldLocalPath); err != nil {
			// 如果删除失败，需要清理新创建的文件
			os.Remove(newLocalPath)
			logger.Log.Error("删除原文件失败",
				logger.String("path", oldLocalPath),
				logger.Error(err))
			return fmt.Errorf("删除原文件失败: %w", err)
		}
	}

	logger.Log.Info("文件重命名成功",
		logger.String("entryName", entryName),
		logger.String("oldPath", oldGouploadPath),
		logger.String("newPath", newGouploadPath),
	)

	return nil
}

// copyFile 复制文件的辅助方法
func (s *GoUploadService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	// 复制文件权限
	sourceInfo, err := os.Stat(src)
	if err != nil {
		return err
	}
	return os.Chmod(dst, sourceInfo.Mode())
}

// === 更新操作方法（上传-删除模式）===

// UpdateThumbnailDraft 更新草稿缩略图（上传新文件后删除旧文件）
func (s *GoUploadService) UpdateThumbnailDraft(ctx context.Context, userID string, reader io.Reader, filename string, size int64, oldGouploadPath string) (*goupload.UploadResult, error) {
	logger.Log.Info("开始更新草稿缩略图",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("oldPath", oldGouploadPath),
	)

	// 1. 先上传新文件
	result, err := s.UploadThumbnailDraft(ctx, userID, reader, filename, size)
	if err != nil {
		logger.Log.Error("上传新缩略图失败", logger.Error(err))
		return nil, err
	}

	// 2. 上传成功后删除旧文件
	if oldGouploadPath != "" {
		if deleteErr := s.DeleteThumbnailDraft(ctx, oldGouploadPath); deleteErr != nil {
			logger.Log.Error("删除旧缩略图失败",
				logger.String("oldPath", oldGouploadPath),
				logger.Error(deleteErr),
			)
			// 注意：删除失败不影响更新操作的成功，只记录错误
		} else {
			logger.Log.Info("旧缩略图删除成功", logger.String("oldPath", oldGouploadPath))
		}
	}

	logger.Log.Info("草稿缩略图更新成功",
		logger.String("userID", userID),
		logger.String("newPath", result.Path),
	)

	return result, nil
}

// UpdateThumbnailFinal 更新最终缩略图（上传新文件后删除旧文件）
func (s *GoUploadService) UpdateThumbnailFinal(ctx context.Context, userID string, reader io.Reader, filename string, size int64, oldGouploadPath string) (*goupload.UploadResult, error) {
	logger.Log.Info("开始更新最终缩略图",
		logger.String("userID", userID),
		logger.String("filename", filename),
		logger.String("oldPath", oldGouploadPath),
	)

	// 1. 先上传新文件
	result, err := s.UploadThumbnailFinal(ctx, userID, reader, filename, size)
	if err != nil {
		logger.Log.Error("上传新最终缩略图失败", logger.Error(err))
		return nil, err
	}

	// 2. 上传成功后删除旧文件
	if oldGouploadPath != "" {
		if deleteErr := s.DeleteThumbnailFinal(ctx, oldGouploadPath); deleteErr != nil {
			logger.Log.Error("删除旧最终缩略图失败",
				logger.String("oldPath", oldGouploadPath),
				logger.Error(deleteErr),
			)
			// 注意：删除失败不影响更新操作的成功，只记录错误
		} else {
			logger.Log.Info("旧最终缩略图删除成功", logger.String("oldPath", oldGouploadPath))
		}
	}

	logger.Log.Info("最终缩略图更新成功",
		logger.String("userID", userID),
		logger.String("newPath", result.Path),
	)

	return result, nil
}
