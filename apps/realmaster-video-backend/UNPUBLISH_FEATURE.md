# 视频下架功能增强

## 功能概述

本次更新增强了视频下架功能，现在不仅在数据库层面添加`unpublished_`前缀，还会在文件系统层面重命名实际文件，确保完全隔离下架的视频内容。

## 主要改进

### 1. 文件系统层面的隔离

**之前的实现：**
- 只在数据库URL中添加`unpublished_`前缀
- 实际文件路径不变，客户端仍可通过缓存的URL访问

**现在的实现：**
- 数据库URL添加`unpublished_`前缀
- 实际文件也重命名，添加`unpublished_`前缀
- 完全隔离下架内容，防止任何形式的访问

### 2. 支持的操作

#### 下架操作 (`"action": "unpublish"`)
- 状态：`Published` → `Unpublished`
- 数据库路径：`USER/2025-28/abc123/video.mp4` → `USER/2025-28/unpublished_abc123/video.mp4`
- 文件系统：`/var/www/media/rm_videos/USER/2025-28/abc123/` → `/var/www/media/rm_videos/USER/2025-28/unpublished_abc123/`

#### 重新发布操作 (`"action": "publish"` 从下架状态)
- 状态：`Unpublished` → `Published`
- 数据库路径：`USER/2025-28/unpublished_abc123/video.mp4` → `USER/2025-28/abc123/video.mp4`
- 文件系统：`/var/www/media/rm_videos/USER/2025-28/unpublished_abc123/` → `/var/www/media/rm_videos/USER/2025-28/abc123/`

## 技术实现

### 新增的服务方法

#### GoUploadService
```go
// 重命名最终视频文件
func (s *GoUploadService) RenameVideoFinal(ctx context.Context, oldGouploadPath, newGouploadPath string) error

// 重命名最终缩略图文件  
func (s *GoUploadService) RenameThumbnailFinal(ctx context.Context, oldGouploadPath, newGouploadPath string) error
```

#### 文件重命名策略
1. **优先使用硬链接**：高效，瞬间完成
2. **回退到文件复制**：跨文件系统时使用
3. **原子性操作**：先创建新文件，成功后删除原文件
4. **错误回滚**：任何步骤失败都会清理已创建的文件

### 修改的业务逻辑

#### VideoService.UpdateVideo()
- 在`unpublish`操作中添加文件重命名调用
- 在`publish`操作（从下架状态）中添加文件重命名调用
- 增强错误处理和日志记录

## 安全性保障

### 1. 多层隔离
- **URL层面**：数据库中的URL包含`unpublished_`前缀
- **文件系统层面**：实际文件路径包含`unpublished_`前缀
- **访问控制**：即使知道原路径也无法访问

### 2. 事务性操作
- 数据库更新和文件重命名在同一事务中
- 任何步骤失败都会回滚整个操作
- 保证数据一致性

### 3. 错误处理
- 详细的错误日志记录
- 文件操作失败时的清理机制
- 用户友好的错误信息

## 测试覆盖

### 单元测试
- ✅ 下架已发布视频的完整流程测试
- ✅ 重新发布已下架视频的完整流程测试
- ✅ 文件系统操作验证
- ✅ 数据库状态验证
- ✅ 错误场景处理

### 测试验证点
1. 视频状态正确转换
2. 数据库路径正确更新
3. 原文件被删除
4. 新文件被创建
5. 文件内容保持不变

## API 使用示例

### 下架视频
```bash
curl -X PATCH "http://localhost:8080/api/videos/{video_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {jwt_token}" \
  -d '{"action": "unpublish"}'
```

### 重新发布视频
```bash
curl -X PATCH "http://localhost:8080/api/videos/{video_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {jwt_token}" \
  -d '{"action": "publish"}'
```

## 部署注意事项

### 1. 权限要求
- 应用程序需要对媒体目录有读写权限
- 确保可以创建新目录和删除旧文件

### 2. 磁盘空间
- 重命名操作可能临时需要额外磁盘空间（文件复制模式）
- 建议监控磁盘使用情况

### 3. 日志监控
- 所有文件操作都有详细日志
- 建议设置日志监控和告警

## 向后兼容性

- ✅ 现有API接口保持不变
- ✅ 现有数据库结构无需修改
- ✅ 现有已发布视频不受影响
- ✅ 现有客户端代码无需修改

## 性能影响

### 优化措施
- 优先使用硬链接（O(1)时间复杂度）
- 只在必要时使用文件复制
- 异步日志记录
- 最小化数据库操作

### 预期性能
- 硬链接模式：几乎瞬间完成
- 文件复制模式：取决于文件大小，通常在秒级
- 数据库操作：毫秒级

## 监控建议

### 关键指标
- 文件重命名操作成功率
- 操作耗时分布
- 磁盘空间使用情况
- 错误率和错误类型

### 告警设置
- 文件操作失败率超过阈值
- 磁盘空间不足
- 操作耗时异常

这个增强功能确保了视频下架的完整性和安全性，防止了任何形式的未授权访问。
